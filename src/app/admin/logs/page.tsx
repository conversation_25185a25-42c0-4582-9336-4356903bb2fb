'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  AlertCircle,
  Search, 
  Download,
  RefreshCw,
  Calendar,
  Activity,
  Database,
  Eye,
  Filter,
  BarChart3
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import { toast } from 'sonner';

interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  admin: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

interface LogStats {
  actions: Array<{ action: string; count: number }>;
  resources: Array<{ resource: string; count: number }>;
}

export default function AuditLogsPage() {
  const { data: session } = useSession();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [selectedResource, setSelectedResource] = useState<string>('');
  const [selectedAdmin, setSelectedAdmin] = useState<string>('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [stats, setStats] = useState<LogStats>({ actions: [], resources: [] });
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    pages: 0,
  });

  const isAdmin = session?.user?.role === 'ADMIN';

  useEffect(() => {
    if (isAdmin) {
      fetchLogs();
    }
  }, [search, selectedAction, selectedResource, selectedAdmin, startDate, endDate, pagination.page, isAdmin]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (search) params.append('search', search);
      if (selectedAction) params.append('action', selectedAction);
      if (selectedResource) params.append('resource', selectedResource);
      if (selectedAdmin) params.append('adminId', selectedAdmin);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await fetch(`/api/admin/logs?${params}`);
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs);
        setPagination(data.pagination);
        setStats(data.stats);
      } else {
        toast.error('Có lỗi xảy ra khi tải logs');
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
      toast.error('Có lỗi xảy ra khi tải logs');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (selectedAction) params.append('action', selectedAction);
      if (selectedResource) params.append('resource', selectedResource);
      if (selectedAdmin) params.append('adminId', selectedAdmin);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await fetch(`/api/admin/logs/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Xuất file thành công');
      } else {
        toast.error('Có lỗi xảy ra khi xuất file');
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi xuất file');
    }
  };

  const getActionBadge = (action: string) => {
    const colors = {
      CREATE: 'bg-green-100 text-green-800',
      UPDATE: 'bg-blue-100 text-blue-800',
      DELETE: 'bg-red-100 text-red-800',
      LOGIN: 'bg-purple-100 text-purple-800',
      LOGOUT: 'bg-gray-100 text-gray-800',
    };
    
    const color = colors[action as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    return <Badge className={color}>{action}</Badge>;
  };

  const getResourceBadge = (resource: string) => {
    const colors = {
      Product: 'bg-orange-100 text-orange-800',
      User: 'bg-indigo-100 text-indigo-800',
      Order: 'bg-yellow-100 text-yellow-800',
      Category: 'bg-pink-100 text-pink-800',
      AdminUser: 'bg-red-100 text-red-800',
    };
    
    const color = colors[resource as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    return <Badge variant="outline" className={color}>{resource}</Badge>;
  };

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold">Không có quyền truy cập</h3>
          <p className="text-muted-foreground">
            Chỉ Admin mới có quyền xem audit logs
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Activity className="h-8 w-8" />
            Audit Logs
          </h1>
          <p className="text-muted-foreground">
            Theo dõi và kiểm tra các hoạt động của admin trong hệ thống
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchLogs}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          <Button onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Xuất CSV
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng logs</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.total}</div>
            <p className="text-xs text-muted-foreground">
              Tổng số bản ghi
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hành động phổ biến</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.actions[0]?.action || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.actions[0]?.count || 0} lần (30 ngày qua)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tài nguyên phổ biến</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.resources[0]?.resource || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.resources[0]?.count || 0} lần (30 ngày qua)
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Bộ lọc
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedAction} onValueChange={setSelectedAction}>
              <SelectTrigger>
                <SelectValue placeholder="Hành động" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                {stats.actions.map((action) => (
                  <SelectItem key={action.action} value={action.action}>
                    {action.action} ({action.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedResource} onValueChange={setSelectedResource}>
              <SelectTrigger>
                <SelectValue placeholder="Tài nguyên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tất cả</SelectItem>
                {stats.resources.map((resource) => (
                  <SelectItem key={resource.resource} value={resource.resource}>
                    {resource.resource} ({resource.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="date"
                placeholder="Từ ngày"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="date"
                placeholder="Đến ngày"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="pl-10"
              />
            </div>

            <Button 
              variant="outline" 
              onClick={() => {
                setSearch('');
                setSelectedAction('');
                setSelectedResource('');
                setSelectedAdmin('');
                setStartDate('');
                setEndDate('');
              }}
            >
              Xóa bộ lọc
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Audit Logs</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Đang tải...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Không có logs</h3>
              <p className="text-muted-foreground">
                Không tìm thấy logs nào phù hợp với bộ lọc
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>Hành động</TableHead>
                    <TableHead>Tài nguyên</TableHead>
                    <TableHead>Admin</TableHead>
                    <TableHead>Mô tả</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead className="text-right">Chi tiết</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="text-sm">
                          {formatDistanceToNow(new Date(log.createdAt), {
                            addSuffix: true,
                            locale: vi,
                          })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(log.createdAt).toLocaleString('vi-VN')}
                        </div>
                      </TableCell>
                      <TableCell>{getActionBadge(log.action)}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {getResourceBadge(log.resource)}
                          {log.resourceId && (
                            <div className="text-xs text-muted-foreground">
                              ID: {log.resourceId}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium">{log.admin.name}</div>
                        <div className="text-xs text-muted-foreground">{log.admin.email}</div>
                        <Badge variant="outline" className="text-xs">
                          {log.admin.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate text-sm">
                          {log.description || '-'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{log.ipAddress || '-'}</div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Chi tiết Audit Log</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Thời gian:</label>
                                  <p className="text-sm">{new Date(log.createdAt).toLocaleString('vi-VN')}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Hành động:</label>
                                  <p className="text-sm">{log.action}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Tài nguyên:</label>
                                  <p className="text-sm">{log.resource}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">ID tài nguyên:</label>
                                  <p className="text-sm">{log.resourceId || '-'}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Admin:</label>
                                  <p className="text-sm">{log.admin.name} ({log.admin.email})</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">IP Address:</label>
                                  <p className="text-sm">{log.ipAddress || '-'}</p>
                                </div>
                              </div>
                              
                              {log.description && (
                                <div>
                                  <label className="text-sm font-medium">Mô tả:</label>
                                  <p className="text-sm">{log.description}</p>
                                </div>
                              )}

                              {log.userAgent && (
                                <div>
                                  <label className="text-sm font-medium">User Agent:</label>
                                  <p className="text-sm break-all">{log.userAgent}</p>
                                </div>
                              )}

                              {log.oldValues && (
                                <div>
                                  <label className="text-sm font-medium">Giá trị cũ:</label>
                                  <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(log.oldValues, null, 2)}
                                  </pre>
                                </div>
                              )}

                              {log.newValues && (
                                <div>
                                  <label className="text-sm font-medium">Giá trị mới:</label>
                                  <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                                    {JSON.stringify(log.newValues, null, 2)}
                                  </pre>
                                </div>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Hiển thị {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} của {pagination.total} logs
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page <= 1}
                  >
                    Trước
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page >= pagination.pages}
                  >
                    Sau
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
