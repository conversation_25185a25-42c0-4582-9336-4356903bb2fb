import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";

const bulkActionSchema = z.object({
  action: z.enum(["mark_read", "mark_unread", "delete"]),
  notificationIds: z.array(z.string()).min(1, "Phải chọn ít nhất một thông báo"),
});

// POST /api/admin/notifications/bulk - Bulk operations on notifications
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, notificationIds } = bulkActionSchema.parse(body);

    // For delete action, only ADMIN can perform it
    if (action === "delete" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa thông báo" },
        { status: 403 }
      );
    }

    // Get notifications to verify access
    const notifications = await prisma.notification.findMany({
      where: {
        id: { in: notificationIds },
      },
    });

    if (notifications.length !== notificationIds.length) {
      return NextResponse.json(
        { error: "Một số thông báo không tồn tại" },
        { status: 400 }
      );
    }

    // Check access for each notification (except for delete by admin)
    if (!(action === "delete" && session.user.role === "ADMIN")) {
      for (const notification of notifications) {
        const hasAccess = 
          notification.targetType === "ALL_ADMINS" ||
          (notification.targetType === "SPECIFIC_ADMIN" && notification.targetId === session.user.id) ||
          notification.targetType === `ROLE_${session.user.role}`;

        if (!hasAccess) {
          return NextResponse.json(
            { error: "Không có quyền thao tác với một số thông báo" },
            { status: 403 }
          );
        }
      }
    }

    let result;

    switch (action) {
      case "mark_read":
        result = await prisma.notification.updateMany({
          where: { id: { in: notificationIds } },
          data: {
            isRead: true,
            readAt: new Date(),
          },
        });
        break;

      case "mark_unread":
        result = await prisma.notification.updateMany({
          where: { id: { in: notificationIds } },
          data: {
            isRead: false,
            readAt: null,
          },
        });
        break;

      case "delete":
        result = await prisma.notification.deleteMany({
          where: { id: { in: notificationIds } },
        });
        break;

      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }

    const actionMessages = {
      mark_read: "Đánh dấu đã đọc",
      mark_unread: "Đánh dấu chưa đọc", 
      delete: "Xóa",
    };

    return NextResponse.json({
      message: `${actionMessages[action]} ${result.count} thông báo thành công`,
      count: result.count,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Bulk notification operation error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}
