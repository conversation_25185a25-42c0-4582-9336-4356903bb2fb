import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { z } from "zod";

const createNotificationSchema = z.object({
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  message: z.string().min(1, "Nội dung là bắt buộc"),
  type: z.enum(["INFO", "SUCCESS", "WARNING", "ERROR", "SYSTEM"]).default("INFO"),
  priority: z.enum(["LOW", "NORMAL", "HIGH", "URGENT"]).default("NORMAL"),
  targetType: z.enum(["ALL_ADMINS", "SPECIFIC_ADMIN", "ROLE_ADMIN", "ROLE_MODERATOR"]).default("ALL_ADMINS"),
  targetId: z.string().optional(),
  actionUrl: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  expiresAt: z.string().datetime().optional(),
});

// GET /api/admin/notifications - Get notifications for current admin
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const unreadOnly = searchParams.get("unreadOnly") === "true";
    const type = searchParams.get("type");

    const skip = (page - 1) * limit;

    // Build where clause for notifications targeting this admin
    const where: any = {
      OR: [
        { targetType: "ALL_ADMINS" },
        { targetType: "SPECIFIC_ADMIN", targetId: session.user.id },
        { targetType: `ROLE_${session.user.role}` },
      ],
      // Only show non-expired notifications
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    };

    if (unreadOnly) {
      where.isRead = false;
    }

    if (type) {
      where.type = type;
    }

    const [notifications, total] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: [
          { priority: "desc" },
          { createdAt: "desc" },
        ],
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
    ]);

    return NextResponse.json({
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get notifications error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách thông báo" },
      { status: 500 }
    );
  }
}

// POST /api/admin/notifications - Create new notification
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can create notifications
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền tạo thông báo" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = createNotificationSchema.parse(body);

    // Validate targetId if targetType is SPECIFIC_ADMIN
    if (data.targetType === "SPECIFIC_ADMIN" && !data.targetId) {
      return NextResponse.json(
        { error: "targetId là bắt buộc khi targetType là SPECIFIC_ADMIN" },
        { status: 400 }
      );
    }

    // Validate targetId exists if provided
    if (data.targetId) {
      const targetAdmin = await prisma.adminUser.findUnique({
        where: { id: data.targetId },
      });

      if (!targetAdmin) {
        return NextResponse.json(
          { error: "Không tìm thấy admin được chỉ định" },
          { status: 400 }
        );
      }
    }

    const notification = await prisma.notification.create({
      data: {
        title: data.title,
        message: data.message,
        type: data.type,
        priority: data.priority,
        targetType: data.targetType,
        targetId: data.targetId,
        actionUrl: data.actionUrl,
        metadata: data.metadata,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
        createdBy: session.user.id,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: "Tạo thông báo thành công",
        notification,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo thông báo" },
      { status: 500 }
    );
  }
}
