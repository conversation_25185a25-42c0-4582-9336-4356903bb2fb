import { test, expect } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminPagesPage } from "./pages/admin-pages.page";
import { 
  generateTestPageData,
  cleanupTestPages,
  getTestAdminId,
  createTestPageInDB
} from "./helpers/pages.helper";

test.describe("Admin Pages Permissions and Edge Cases", () => {
  let adminLoginPage: AdminLoginPage;
  let adminPagesPage: AdminPagesPage;

  test.beforeEach(async ({ page }) => {
    adminLoginPage = new AdminLoginPage(page);
    adminPagesPage = new AdminPagesPage(page);
    await cleanupTestPages();
  });

  test.afterEach(async () => {
    await cleanupTestPages();
  });

  test.describe("Moderator Permissions", () => {
    test("should allow moderator to create pages", async () => {
      // Login as moderator
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsModerator();

      const testPageData = generateTestPageData({
        title: "Moderator Created Page",
        content: "<p>Page created by moderator</p>"
      });

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();
      await adminPagesPage.goToCreatePage();
      await adminPagesPage.fillPageForm(testPageData);
      await adminPagesPage.submitForm();

      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.pageExists(testPageData.title)).resolves.toBe(true);
    });

    test("should allow moderator to edit pages", async () => {
      // Create page as admin first
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Page for Moderator Edit",
        content: "<p>Original content</p>"
      });
      await createTestPageInDB(testPage, adminId);

      // Login as moderator
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsModerator();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();
      await adminPagesPage.editPage(testPage.title);

      // Update the page
      await adminPagesPage.titleInput.fill("Updated by Moderator");
      await adminPagesPage.submitForm();

      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.pageExists("Updated by Moderator")).resolves.toBe(true);
    });

    test("should allow moderator to delete pages", async () => {
      // Create page as admin first
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Page for Moderator Delete",
        content: "<p>To be deleted</p>"
      });
      await createTestPageInDB(testPage, adminId);

      // Login as moderator
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsModerator();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();
      await adminPagesPage.deletePage(testPage.title);

      await expect(adminPagesPage.pageExists(testPage.title)).resolves.toBe(false);
    });
  });

  test.describe("Edge Cases and Error Handling", () => {
    test("should handle network errors gracefully", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Simulate network failure by going offline
      await adminPagesPage.page.context().setOffline(true);

      await adminPagesPage.goToCreatePage();
      
      const testPageData = generateTestPageData({
        title: "Network Error Test Page"
      });

      await adminPagesPage.fillPageForm(testPageData);
      await adminPagesPage.submitForm();

      // Should show error message for network failure
      await adminPagesPage.expectErrorToast();

      // Restore network
      await adminPagesPage.page.context().setOffline(false);
    });

    test("should handle very long content", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      const longContent = "<p>" + "Very long content. ".repeat(1000) + "</p>";
      const testPageData = generateTestPageData({
        title: "Long Content Test Page",
        content: longContent
      });

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();
      await adminPagesPage.fillPageForm(testPageData);
      await adminPagesPage.submitForm();

      // Should handle long content successfully
      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.pageExists(testPageData.title)).resolves.toBe(true);
    });

    test("should handle special characters in title and slug", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      const testPageData = generateTestPageData({
        title: "Trang với ký tự đặc biệt: áéíóú & @#$%",
        content: "<p>Content with special characters</p>",
        slug: "trang-voi-ky-tu-dac-biet"
      });

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();
      await adminPagesPage.fillPageForm(testPageData);
      await adminPagesPage.submitForm();

      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.pageExists(testPageData.title)).resolves.toBe(true);
    });

    test("should handle empty search results", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Search for non-existent content
      await adminPagesPage.searchPages("NonExistentPageTitle12345");

      // Should show empty state or no results message
      const pageCount = await adminPagesPage.getPageCount();
      expect(pageCount).toBe(0);
    });

    test("should handle concurrent page creation", async ({ browser }) => {
      // Create two browser contexts to simulate concurrent users
      const context1 = await browser.newContext();
      const context2 = await browser.newContext();
      
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();

      const adminLoginPage1 = new AdminLoginPage(page1);
      const adminLoginPage2 = new AdminLoginPage(page2);
      const adminPagesPage1 = new AdminPagesPage(page1);
      const adminPagesPage2 = new AdminPagesPage(page2);

      // Login both users
      await adminLoginPage1.goto();
      await adminLoginPage1.waitForPageLoad();
      await adminLoginPage1.loginAsAdmin();

      await adminLoginPage2.goto();
      await adminLoginPage2.waitForPageLoad();
      await adminLoginPage2.loginAsAdmin();

      // Both try to create pages with similar slugs
      const testPageData1 = generateTestPageData({
        title: "Concurrent Test Page 1",
        slug: "concurrent-test-page"
      });

      const testPageData2 = generateTestPageData({
        title: "Concurrent Test Page 2",
        slug: "concurrent-test-page" // Same slug
      });

      // Start creation process simultaneously
      await Promise.all([
        (async () => {
          await adminPagesPage1.goto();
          await adminPagesPage1.goToCreatePage();
          await adminPagesPage1.fillPageForm(testPageData1);
          await adminPagesPage1.submitForm();
        })(),
        (async () => {
          await adminPagesPage2.goto();
          await adminPagesPage2.goToCreatePage();
          await adminPagesPage2.fillPageForm(testPageData2);
          await adminPagesPage2.submitForm();
        })()
      ]);

      // One should succeed, one should fail with duplicate slug error
      // The system should handle this gracefully
      
      await context1.close();
      await context2.close();
    });

    test("should handle page creation with missing required data", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Fill only title, leave content empty
      await adminPagesPage.titleInput.fill("Incomplete Page");
      await adminPagesPage.submitForm();

      // Should show validation error for missing content
      await expect(adminPagesPage.page.locator('.text-red-500, .error-message').filter({ hasText: /Nội dung.*bắt buộc/ })).toBeVisible();
    });

    test("should handle session timeout during form submission", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Fill form
      const testPageData = generateTestPageData({
        title: "Session Timeout Test Page"
      });
      await adminPagesPage.fillPageForm(testPageData);

      // Clear cookies to simulate session timeout
      await adminPagesPage.page.context().clearCookies();

      // Try to submit
      await adminPagesPage.submitForm();

      // Should redirect to login or show authentication error
      await expect(adminPagesPage.page).toHaveURL(/\/admin\/auth\/signin/);
    });

    test("should handle browser back/forward navigation", async () => {
      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Navigate to create page
      await adminPagesPage.goToCreatePage();
      await expect(adminPagesPage.page).toHaveURL("/admin/pages/create");

      // Go back
      await adminPagesPage.page.goBack();
      await expect(adminPagesPage.page).toHaveURL("/admin/pages");

      // Go forward
      await adminPagesPage.page.goForward();
      await expect(adminPagesPage.page).toHaveURL("/admin/pages/create");

      // Form should still be functional
      await expect(adminPagesPage.titleInput).toBeVisible();
    });

    test("should handle page refresh during form editing", async () => {
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Refresh Test Page",
        content: "<p>Original content</p>"
      });
      await createTestPageInDB(testPage, adminId);

      await adminLoginPage.goto();
      await adminLoginPage.waitForPageLoad();
      await adminLoginPage.loginAsAdmin();

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();
      await adminPagesPage.editPage(testPage.title);

      // Make some changes
      await adminPagesPage.titleInput.fill("Modified Title");

      // Refresh the page
      await adminPagesPage.page.reload();

      // Should reload the original data, not the modified data
      await adminPagesPage.waitForFormLoad();
      const titleValue = await adminPagesPage.titleInput.inputValue();
      expect(titleValue).toBe(testPage.title);
    });
  });
});
