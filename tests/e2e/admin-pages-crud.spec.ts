import { test, expect } from "@playwright/test";
import { AdminLoginPage } from "./pages/admin-login.page";
import { AdminPagesPage } from "./pages/admin-pages.page";
import {
  generateTestPageData,
  generateTestPages,
  cleanupTestPages,
  getTestAdminId,
  createTestPageInDB,
  verifyPageExistsInDB,
  verifyPageNotExistsInDB,
  getPageBySlug,
} from "./helpers/pages.helper";
import {
  PageFormValidator,
  RichTextEditorHelper,
  PageStatusHelper,
  BulkOperationsHelper,
  SearchFilterHelper,
} from "./helpers/page-form.helper";

test.describe("Admin Pages CRUD Operations", () => {
  let adminLoginPage: AdminLoginPage;
  let adminPagesPage: AdminPagesPage;
  let pageFormValidator: PageFormValidator;
  let richTextEditor: RichTextEditorHelper;
  let pageStatusHelper: PageStatusHelper;
  let bulkOperations: BulkOperationsHelper;
  let searchFilter: SearchFilterHelper;

  test.beforeEach(async ({ page }) => {
    // Initialize page objects
    adminLoginPage = new AdminLoginPage(page);
    adminPagesPage = new AdminPagesPage(page);
    pageFormValidator = new PageFormValidator(page);
    richTextEditor = new RichTextEditorHelper(page);
    pageStatusHelper = new PageStatusHelper(page);
    bulkOperations = new BulkOperationsHelper(page);
    searchFilter = new SearchFilterHelper(page);

    // Clean up any existing test data
    await cleanupTestPages();

    // Login as admin
    await adminLoginPage.goto();
    await adminLoginPage.waitForPageLoad();
    await adminLoginPage.loginAsAdmin();
  });

  test.afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestPages();
  });

  test.describe("Create Page Operations", () => {
    test("should create a new page with minimal fields", async () => {
      const testPageData = generateTestPageData({
        title: "E2E Test Page - Simple",
        content: "<p>This is a simple test page.</p>",
        status: "DRAFT",
        featured: false,
      });

      // Navigate to create page
      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();
      await adminPagesPage.goToCreatePage();

      // Fill only required fields
      await adminPagesPage.titleInput.fill(testPageData.title);
      await richTextEditor.setContent(testPageData.content);

      // Submit form
      await adminPagesPage.submitForm();

      // Wait for redirect or success indication
      await adminPagesPage.page.waitForTimeout(3000);

      // Check if we're back on the pages list or if there's a success message
      const currentUrl = adminPagesPage.page.url();
      console.log("Current URL after submission:", currentUrl);

      // Check for any toast messages
      const toastElements = await adminPagesPage.page
        .locator('div[class*="bg-green"], div[class*="bg-red"], .toast')
        .all();
      for (const toast of toastElements) {
        const text = await toast.textContent();
        console.log("Toast found:", text);
      }
    });

    test("should create a page with minimal required fields", async () => {
      const testPageData = generateTestPageData({
        title: "E2E Test Page - Minimal",
        content: "<p>Minimal test page content.</p>",
        status: "DRAFT",
        featured: false,
      });

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Fill only required fields
      await adminPagesPage.titleInput.fill(testPageData.title);
      await richTextEditor.setContent(testPageData.content);
      await adminPagesPage.submitForm();

      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.pageExists(testPageData.title)).resolves.toBe(
        true
      );
    });

    test("should show validation errors for empty required fields", async () => {
      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Try to submit empty form
      await adminPagesPage.submitForm();

      // Verify validation errors
      await pageFormValidator.expectRequiredFieldErrors();

      // Form should not be submitted
      await expect(adminPagesPage.page).toHaveURL(/\/admin\/pages\/create/);
    });

    test("should auto-generate slug from title", async () => {
      const testTitle = "E2E Test Page Auto Slug";

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Fill title and check if slug is auto-generated
      await adminPagesPage.titleInput.fill(testTitle);
      await adminPagesPage.titleInput.blur(); // Trigger slug generation

      // Wait for slug to be generated
      await adminPagesPage.page.waitForTimeout(500);

      const slugValue = await adminPagesPage.slugInput.inputValue();
      expect(slugValue).toMatch(/e2e-test-page-auto-slug/);
    });

    test("should handle duplicate slug error", async () => {
      const adminId = await getTestAdminId();
      const existingPage = generateTestPageData({
        title: "Existing Page",
        slug: "duplicate-slug-test",
      });

      // Create existing page in database
      await createTestPageInDB(existingPage, adminId);

      // Try to create new page with same slug
      const newPageData = generateTestPageData({
        title: "New Page",
        slug: "duplicate-slug-test",
      });

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();
      await adminPagesPage.fillPageForm(newPageData);
      await adminPagesPage.submitForm();

      // Should show error for duplicate slug
      await adminPagesPage.expectErrorToast();
    });
  });

  test.describe("Read Page Operations", () => {
    test("should display pages list with correct information", async () => {
      const adminId = await getTestAdminId();
      const testPages = generateTestPages(3, { title: "E2E List Test Page" });

      // Create test pages in database
      for (const pageData of testPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Verify pages are displayed
      for (const pageData of testPages) {
        await expect(adminPagesPage.pageExists(pageData.title)).resolves.toBe(
          true
        );
      }

      // Verify page count
      const pageCount = await adminPagesPage.getPageCount();
      expect(pageCount).toBeGreaterThanOrEqual(3);
    });

    test("should search pages by title", async () => {
      const adminId = await getTestAdminId();
      const searchablePages = [
        generateTestPageData({ title: "Searchable Page One" }),
        generateTestPageData({ title: "Searchable Page Two" }),
        generateTestPageData({ title: "Different Page" }),
      ];

      for (const pageData of searchablePages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Search for "Searchable"
      await searchFilter.searchPages("Searchable");

      // Should show only searchable pages
      await expect(
        adminPagesPage.pageExists("Searchable Page One")
      ).resolves.toBe(true);
      await expect(
        adminPagesPage.pageExists("Searchable Page Two")
      ).resolves.toBe(true);
      await expect(adminPagesPage.pageExists("Different Page")).resolves.toBe(
        false
      );
    });

    test("should filter pages by status", async () => {
      const adminId = await getTestAdminId();
      const statusPages = [
        generateTestPageData({ title: "Draft Page", status: "DRAFT" }),
        generateTestPageData({ title: "Published Page", status: "PUBLISHED" }),
        generateTestPageData({ title: "Archived Page", status: "ARCHIVED" }),
      ];

      for (const pageData of statusPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Filter by PUBLISHED status
      await searchFilter.filterByStatus("PUBLISHED");

      // Should show only published pages
      await expect(adminPagesPage.pageExists("Published Page")).resolves.toBe(
        true
      );
      await expect(adminPagesPage.pageExists("Draft Page")).resolves.toBe(
        false
      );
      await expect(adminPagesPage.pageExists("Archived Page")).resolves.toBe(
        false
      );
    });

    test("should filter pages by featured status", async () => {
      const adminId = await getTestAdminId();
      const featuredPages = [
        generateTestPageData({ title: "Featured Page", featured: true }),
        generateTestPageData({ title: "Regular Page", featured: false }),
      ];

      for (const pageData of featuredPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Filter by featured
      await searchFilter.filterByFeatured(true);

      // Should show only featured pages
      await expect(adminPagesPage.pageExists("Featured Page")).resolves.toBe(
        true
      );
      await expect(adminPagesPage.pageExists("Regular Page")).resolves.toBe(
        false
      );
    });
  });

  test.describe("Update Page Operations", () => {
    test("should edit existing page", async () => {
      const adminId = await getTestAdminId();
      const originalPage = generateTestPageData({
        title: "Original Page Title",
        content: "<p>Original content</p>",
        status: "DRAFT",
      });

      const createdPage = await createTestPageInDB(originalPage, adminId);

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Edit the page
      await adminPagesPage.editPage(originalPage.title);

      // Update fields
      const updatedData = {
        title: "Updated Page Title",
        content: "<p>Updated content with more information</p>",
        status: "PUBLISHED" as const,
        featured: true,
      };

      await adminPagesPage.titleInput.fill(updatedData.title);
      await richTextEditor.setContent(updatedData.content);
      await pageStatusHelper.setStatus(updatedData.status);
      await pageStatusHelper.toggleFeatured(updatedData.featured);

      await adminPagesPage.submitForm();

      // Verify success
      await adminPagesPage.expectSuccessToast();
      await expect(adminPagesPage.page).toHaveURL("/admin/pages");

      // Verify updated page exists in list
      await expect(adminPagesPage.pageExists(updatedData.title)).resolves.toBe(
        true
      );

      // Verify changes in database
      const updatedPageFromDB = await getPageBySlug(createdPage.slug!);
      expect(updatedPageFromDB?.title).toBe(updatedData.title);
      expect(updatedPageFromDB?.status).toBe(updatedData.status);
      expect(updatedPageFromDB?.featured).toBe(updatedData.featured);
    });

    test("should toggle featured status from list", async () => {
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Toggle Featured Test",
        featured: false,
      });

      await createTestPageInDB(testPage, adminId);

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Toggle featured status
      await adminPagesPage.toggleFeatured(testPage.title);

      // Verify success
      await adminPagesPage.expectSuccessToast();

      // Verify change in database
      const updatedPage = await getPageBySlug(testPage.slug!);
      expect(updatedPage?.featured).toBe(true);
    });
  });

  test.describe("Delete Page Operations", () => {
    test("should delete single page", async () => {
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Page to Delete",
        content: "<p>This page will be deleted</p>",
      });

      await createTestPageInDB(testPage, adminId);

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Verify page exists before deletion
      await expect(adminPagesPage.pageExists(testPage.title)).resolves.toBe(
        true
      );

      // Delete the page
      await adminPagesPage.deletePage(testPage.title);

      // Verify page is removed from list
      await expect(adminPagesPage.pageExists(testPage.title)).resolves.toBe(
        false
      );

      // Verify page is deleted from database
      await expect(verifyPageNotExistsInDB(testPage.slug!)).resolves.toBe(true);
    });

    test("should show confirmation dialog before deletion", async () => {
      const adminId = await getTestAdminId();
      const testPage = generateTestPageData({
        title: "Confirmation Test Page",
      });

      await createTestPageInDB(testPage, adminId);

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Click delete button
      const row = adminPagesPage.getPageRow(testPage.title);
      await row.locator(adminPagesPage.deleteButton).click();

      // Verify confirmation dialog appears
      await adminPagesPage.expectConfirmDialog();

      // Cancel deletion
      await adminPagesPage.cancelDeleteButton.click();

      // Verify page still exists
      await expect(adminPagesPage.pageExists(testPage.title)).resolves.toBe(
        true
      );
      await expect(verifyPageExistsInDB(testPage.slug!)).resolves.toBe(true);
    });

    test("should handle deletion error gracefully", async () => {
      // Try to delete non-existent page (simulate error)
      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // This test would require mocking API error response
      // For now, we'll test the UI behavior when delete fails
      // In a real scenario, you might mock the API response
    });
  });

  test.describe("Bulk Operations", () => {
    test("should select and bulk delete multiple pages", async () => {
      const adminId = await getTestAdminId();
      const testPages = generateTestPages(3, {
        title: "Bulk Delete Test Page",
      });

      for (const pageData of testPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Select pages for bulk deletion
      await bulkOperations.selectPagesByTitles([
        testPages[0].title,
        testPages[1].title,
      ]);

      // Verify selection count
      const selectedCount = await bulkOperations.getSelectedCount();
      expect(selectedCount).toBe(2);

      // Bulk delete
      await bulkOperations.bulkDeleteSelected();

      // Verify pages are deleted
      await expect(adminPagesPage.pageExists(testPages[0].title)).resolves.toBe(
        false
      );
      await expect(adminPagesPage.pageExists(testPages[1].title)).resolves.toBe(
        false
      );
      await expect(adminPagesPage.pageExists(testPages[2].title)).resolves.toBe(
        true
      ); // Not selected

      // Verify database deletion
      await expect(verifyPageNotExistsInDB(testPages[0].slug!)).resolves.toBe(
        true
      );
      await expect(verifyPageNotExistsInDB(testPages[1].slug!)).resolves.toBe(
        true
      );
      await expect(verifyPageExistsInDB(testPages[2].slug!)).resolves.toBe(
        true
      );
    });

    test("should select all pages", async () => {
      const adminId = await getTestAdminId();
      const testPages = generateTestPages(3, { title: "Select All Test Page" });

      for (const pageData of testPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Select all pages
      await bulkOperations.selectAllPages();

      // Verify all pages are selected
      const selectedCount = await bulkOperations.getSelectedCount();
      const totalCount = await adminPagesPage.getPageCount();
      expect(selectedCount).toBe(totalCount);
    });

    test("should deselect all pages", async () => {
      const adminId = await getTestAdminId();
      const testPages = generateTestPages(2, { title: "Deselect Test Page" });

      for (const pageData of testPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Select all then deselect
      await bulkOperations.selectAllPages();
      await bulkOperations.deselectAllPages();

      // Verify no pages are selected
      const selectedCount = await bulkOperations.getSelectedCount();
      expect(selectedCount).toBe(0);
    });

    test("should bulk change status of selected pages", async () => {
      const adminId = await getTestAdminId();
      const testPages = generateTestPages(2, {
        title: "Bulk Status Test Page",
        status: "DRAFT",
      });

      for (const pageData of testPages) {
        await createTestPageInDB(pageData, adminId);
      }

      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Select pages
      await bulkOperations.selectPagesByTitles([
        testPages[0].title,
        testPages[1].title,
      ]);

      // Bulk change status to PUBLISHED
      await bulkOperations.bulkChangeStatus("PUBLISHED");

      // Verify status change in database
      for (const pageData of testPages) {
        const updatedPage = await getPageBySlug(pageData.slug!);
        expect(updatedPage?.status).toBe("PUBLISHED");
      }
    });
  });

  test.describe("Form Validation", () => {
    test("should validate required fields", async () => {
      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Submit empty form
      await adminPagesPage.submitForm();

      // Check validation errors
      await pageFormValidator.expectRequiredFieldErrors();
    });

    test("should validate slug format", async () => {
      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      // Fill form with invalid slug
      await adminPagesPage.titleInput.fill("Test Page");
      await richTextEditor.setContent("<p>Test content</p>");
      await adminPagesPage.slugInput.fill("invalid slug with spaces!");

      await adminPagesPage.submitForm();

      // Should show slug format error
      await pageFormValidator.expectSlugFormatError();
    });

    test("should prevent XSS in content", async () => {
      const maliciousContent =
        '<script>alert("XSS")</script><p>Safe content</p>';

      await adminPagesPage.goto();
      await adminPagesPage.goToCreatePage();

      await adminPagesPage.titleInput.fill("XSS Test Page");
      await richTextEditor.setContent(maliciousContent);

      await adminPagesPage.submitForm();

      // Form should submit successfully (content will be sanitized server-side)
      await adminPagesPage.expectSuccessToast();

      // Verify the page was created but script tags were removed
      const createdPage = await getPageBySlug("xss-test-page");
      expect(createdPage?.content).not.toContain("<script>");
      expect(createdPage?.content).toContain("<p>Safe content</p>");
    });
  });

  test.describe("Authentication and Authorization", () => {
    test("should redirect to login when not authenticated", async ({
      page,
    }) => {
      // Clear session/cookies to simulate unauthenticated state
      await page.context().clearCookies();

      // Try to access admin pages directly
      await page.goto("/admin/pages");

      // Should redirect to login
      await expect(page).toHaveURL(/\/admin\/auth\/signin/);
    });

    test("should maintain session across page navigation", async () => {
      // Already logged in from beforeEach
      await adminPagesPage.goto();
      await adminPagesPage.waitForPageLoad();

      // Navigate to create page
      await adminPagesPage.goToCreatePage();

      // Should still be authenticated
      await expect(adminPagesPage.page).toHaveURL("/admin/pages/create");
      await expect(adminPagesPage.titleInput).toBeVisible();
    });
  });
});
